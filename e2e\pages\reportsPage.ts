import { Page, Locator } from '@playwright/test';

export class ReportsPage {
  readonly page: Page;

  // Side menu
  readonly selectCategoryText: Locator;
  readonly unspscOption: Locator;
  readonly unspscCategory: Locator;
  readonly vccOption: Locator;
  readonly vscOption: Locator;
  readonly vizientContractCategory: Locator;
  readonly codeGroupAndSubcategory: Locator;
  readonly vendorSelectionText: Locator;
  readonly requiredHeading: Locator;

  // Modal icons
  readonly categorizationRankIcon: Locator;
  readonly confidenceLabelIcon: Locator;

  // Report results
  readonly backArrow: Locator;
  readonly dashboardCustomize: Locator;
  readonly resultsHeading: Locator;
  readonly resultsLabel: Locator;
  readonly exportsLabel: Locator;
  readonly keywordFilterInput: Locator;

  // Pagination
  readonly itemsPerPageText: Locator;
  readonly itemsPerPageValue: Locator;
  readonly paginationLabel: Locator;

  // Modal buttons
  readonly okButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // Side menu
    this.selectCategoryText = page.getByText('Select a Category');
    this.unspscOption = page.getByText('UNSPSC', { exact: true });
    this.unspscCategory = page.getByText('UNSPSC Category');
    this.vccOption = page.getByText('VCC');
    this.vscOption = page.getByText('VSC');
    this.vizientContractCategory = page.getByText('Vizient Contract Category', { exact: true });
    this.codeGroupAndSubcategory = page.getByText('Code, Group, and Subcategory');
    this.vendorSelectionText = page.getByText('Vendor Selection');
    this.requiredHeading = page.getByRole('heading', {
      name: '*Input Vendors and Output Vendors are required',
    });

    // Modal icons
    this.categorizationRankIcon = page.locator('#categorizationRankIcon');
    this.confidenceLabelIcon = page.locator('#confidenceLabelIcon');

    // Report results
    this.backArrow = page.getByRole('button').filter({ hasText: 'arrow_back_ios' });
    this.resultsHeading = page.getByRole('heading', { name: 'Crosses, Category, and' });
    this.resultsLabel = page.getByText('Results', { exact: true });
    this.exportsLabel = page.getByText('My Exports');
    this.keywordFilterInput = page.getByRole('textbox', {
      name: 'Type keyword to filter results',
    });
    this.dashboardCustomize = page.getByRole('button', { name: 'dashboard_customize' });

    // Pagination
    this.itemsPerPageText = page.getByText('Items per page:');
    this.itemsPerPageValue = page.locator('div').filter({ hasText: /^250$/ }).nth(2);
    this.paginationLabel = page.getByText('of 0');

    // OK button for modals
    this.okButton = page.getByRole('button', { name: 'OK' });
  }

  readonly gridHeaders = [
    'Input Product Key',
    'Input Vendor Part Number',
    'Input Vendor Name',
    'Input Vendor Product Description',
    'Input MFG or Dist Type',
    'Output Product Key',
    'Output Vendor Part Number',
    'Output Vendor Name',
    'Output Vendor Product Description',
    'Output MFG or Dist Type',
    'Number of Sources',
    'Sources',
    'Categorization Rank',
    'Confidence Label',
    'Input Vizient Contract Category',
    'Output Vizient Contract Category',
    'Input Contract Number',
    'Input Active on National Contract',
    'Output Contract Number',
    'Output Active on National Contract',
    'Is Valid',
  ];
}