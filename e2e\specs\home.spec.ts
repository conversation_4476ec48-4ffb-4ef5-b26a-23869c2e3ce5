import { test, expect } from '../fixtures/auth';
import { HomePage } from '../pages/homePage';

test.describe('Home page validations', () => {
  test('Check data in the banner', async ({ context }) => {
    const page = await context.newPage();
    const home = new HomePage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');

    // 🔹 Validate header
    await expect(home.headerTitle, 'Expected banner header to be visible').toBeVisible({ timeout: 10000 });
    await expect(home.headerTitle).toHaveText('Enterprise Cross-Reference Consumption');

    // 🔹 Validate Home button
    await expect(home.homeButton, 'Expected Home button to be visible').toBeVisible({ timeout: 10000 });
    await expect(home.homeButton).toHaveText('Home');
  });
});
