import { Page, Locator, expect } from '@playwright/test';

export class LineItemPage {
  readonly page: Page;

  // Main navigation
  readonly lineItemButton: Locator;

  // Grid controls
  readonly gridTitle: Locator;
  readonly searchInput: Locator;
  readonly uploadButton: Locator;
  readonly refreshButton: Locator;
  readonly downloadTemplateLink: Locator;

  // Footer
  readonly contactLink: Locator;
  readonly footerText: Locator;
  readonly copyrightText: Locator;
  readonly itemsPerPageLabel: Locator;
  readonly itemsPerPageValue: Locator;
  readonly overlayBackdrop: Locator;
  readonly paginationText: Locator;
  readonly nextPageButton: Locator;
  readonly lastPageButton: Locator;

  constructor(page: Page) {
    this.page = page;

    this.lineItemButton = page.getByRole('button', { name: 'Line-Item List Cross-References' });

    this.gridTitle = page.getByRole('heading', { name: 'Line-Item List' });
    this.searchInput = page.getByRole('textbox', { name: 'Type keyword to filter results' });
    this.uploadButton = page.getByRole('button', { name: 'Upload File' });
    this.refreshButton = page.getByRole('button', { name: 'Refresh' });
    this.downloadTemplateLink = page.getByRole('link', { name: 'Download Template' });

    this.contactLink = page.getByRole('link', { name: 'Contact' });
    this.footerText = page.locator('#footer').getByText('Enterprise Cross-Reference');
    this.copyrightText = page.getByText('© 2025 Vizient Inc. All');
    this.itemsPerPageLabel = page.getByText('Items per page:');
    this.itemsPerPageValue = page.locator('div').filter({ hasText: /^25$/ }).nth(2);
    this.overlayBackdrop = page.locator('.cdk-overlay-backdrop');
    this.paginationText = page.getByText('1 – 25 of 268');
    this.nextPageButton = page.getByRole('button', { name: 'Next page' });
    this.lastPageButton = page.getByRole('button', { name: 'Last page' });
  }

  // Grid column headers
  readonly gridHeaders = [
    { role: 'button', name: 'Line-Item List ID' },
    { role: 'columnheader', name: 'File Name' },
    { role: 'button', name: 'Upload Date' },
    { role: 'button', name: 'Process Date' },
    { role: 'button', name: 'Record Count' },
    { role: 'button', name: 'Status' },
    { role: 'button', name: 'User' },
    { role: 'columnheader', name: 'Download Results File' },
    { role: 'button', name: 'Download Error File' },
  ];
}