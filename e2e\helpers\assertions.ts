import { Page, expect } from '@playwright/test';

export async function expectTextsVisible(
  page: Page,
  texts: string[],
  timeout = 15000,
  preWaitMs = 0
) {
  if (preWaitMs > 0) {
    console.log(`⏱ Waiting ${preWaitMs}ms before checking text visibility...`);
    await page.waitForTimeout(preWaitMs);
  }

  for (const text of texts) {
    try {
      console.log(`🔍 Checking text: "${text}"`);
      await expect(page.getByText(text, { exact: true })).toBeVisible({ timeout });
    } catch (error) {
      console.error(`❌ FAILED to find visible text: "${text}"`);
      throw error;
    }
  }
}