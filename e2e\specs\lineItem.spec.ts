import { test, expect } from '../fixtures/auth';
import { LineItemPage } from '../pages/lineItemPage';

test.describe('Line-Item page validations', () => {
  test('Check data in list grid', async ({ context }) => {
    const page = await context.newPage();
    const lineItem = new LineItemPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');

    // 🔹 Navigation
    await expect(lineItem.lineItemButton).toBeVisible({ timeout: 10000 });
    await expect(lineItem.lineItemButton).toHaveText('Line-Item List Cross-References');
    await lineItem.lineItemButton.click();

    // 🔹 Grid Controls
    await expect(lineItem.gridTitle).toBeVisible();
    await expect(lineItem.searchInput).toBeVisible();
    await expect(lineItem.uploadButton).toBeVisible();
    await expect(lineItem.refreshButton).toBeVisible();
    await expect(lineItem.downloadTemplateLink).toBeVisible();

    // 🔹 Column Headers
    for (const header of lineItem.gridHeaders) {
      await expect(page.getByRole(header.role as any, { name: header.name })).toBeVisible();
    }

    // 🔹 Footer
    await expect(lineItem.contactLink).toBeVisible();
    await expect(lineItem.footerText).toBeVisible();
    await expect(lineItem.copyrightText).toBeVisible();
    await expect(lineItem.itemsPerPageLabel).toBeVisible();

    await lineItem.itemsPerPageValue.click();
    await lineItem.overlayBackdrop.click();

    await expect(lineItem.paginationText).toBeVisible();
    await lineItem.nextPageButton.click();
    await expect(lineItem.lastPageButton).toBeVisible();
  });
});