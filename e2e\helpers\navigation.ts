import { Page, expect } from '@playwright/test';

export async function navigateToCCCReport(page: Page) {
  await page.goto('https://mdmxref.test.vizientinc.com/');
  await page.getByRole('button', { name: 'Reports' }).click();
  await expect(page.getByRole('menuitem', { name: 'Crosses, Category, and Confidence' })).toBeVisible();
  await page.getByRole('menuitem', { name: 'Crosses, Category, and Confidence' }).click();
}