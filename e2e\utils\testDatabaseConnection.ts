// e2e/utils/testDatabaseConnection.ts
import { config } from 'dotenv';
import { DatabaseConnection } from './database';
import { getDatabaseConfig, getMSSQLConfig } from '../config/database';

// Load environment variables
config();

async function testDatabaseConnection() {
  console.log('🔧 Testing database connection...');
  
  try {
    // Display configuration (without sensitive data)
    const dbConfig = getDatabaseConfig();
    console.log('📋 Database Configuration:');
    console.log(`   Server: ${dbConfig.server}`);
    console.log(`   Database: ${dbConfig.database}`);
    console.log(`   Port: ${dbConfig.port}`);
    console.log(`   User: ${dbConfig.user ? '***configured***' : 'not configured'}`);
    console.log(`   Password: ${dbConfig.password ? '***configured***' : 'not configured'}`);
    console.log(`   Authentication Type: ${dbConfig.authentication?.type || 'default'}`);
    console.log(`   Encrypt: ${dbConfig.options?.encrypt}`);
    console.log(`   Trust Server Certificate: ${dbConfig.options?.trustServerCertificate}`);
    
    // Test connection
    const db = DatabaseConnection.getInstance();
    console.log('\n🔌 Attempting to connect...');
    
    await db.connect();
    console.log('✅ Database connection established successfully');
    
    // Test basic query
    console.log('\n🔍 Testing basic query...');
    const result = await db.queryScalar<number>('SELECT 1 as test');
    
    if (result === 1) {
      console.log('✅ Basic query test passed');
    } else {
      console.log('❌ Basic query test failed');
      return false;
    }
    
    // Test database schema access
    console.log('\n📊 Testing database schema access...');
    try {
      const tableCount = await db.queryScalar<number>(`
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
      `);
      
      console.log(`✅ Found ${tableCount} tables in database`);
      
      // List some tables
      const tables = await db.query<{TABLE_NAME: string}>(`
        SELECT TOP 10 TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
      `);
      
      if (tables.length > 0) {
        console.log('📋 Sample tables found:');
        tables.forEach(table => {
          console.log(`   - ${table.TABLE_NAME}`);
        });
      }
      
    } catch (error) {
      console.log('⚠️ Schema access test failed (may be permission-related):', error);
    }
    
    // Test specific MDMXref tables
    console.log('\n🔍 Testing MDMXref-specific tables...');
    const expectedTables = ['Products', 'Vendors', 'ContractCategories', 'CrossReferences'];
    
    for (const tableName of expectedTables) {
      try {
        const exists = await db.queryScalar<number>(`
          SELECT COUNT(*) 
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_NAME = @tableName
        `, { tableName });
        
        if (exists > 0) {
          console.log(`✅ Table '${tableName}' found`);
          
          // Test record count
          try {
            const recordCount = await db.queryScalar<number>(`SELECT COUNT(*) FROM [${tableName}]`);
            console.log(`   📊 Records in ${tableName}: ${recordCount}`);
          } catch (error) {
            console.log(`   ⚠️ Could not count records in ${tableName}:`, error);
          }
        } else {
          console.log(`❌ Table '${tableName}' not found`);
        }
      } catch (error) {
        console.log(`❌ Error checking table '${tableName}':`, error);
      }
    }
    
    await db.disconnect();
    console.log('\n✅ Database connection test completed successfully');
    return true;
    
  } catch (error) {
    console.error('\n❌ Database connection test failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      
      // Provide helpful error messages
      if (error.message.includes('Login failed')) {
        console.error('\n💡 Suggestion: Check your username and password in the .env file');
      } else if (error.message.includes('Cannot open server')) {
        console.error('\n💡 Suggestion: Check the server name and network connectivity');
      } else if (error.message.includes('timeout')) {
        console.error('\n💡 Suggestion: Check network connectivity and firewall settings');
      }
    }
    
    return false;
  }
}

// Run the test if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testDatabaseConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

export { testDatabaseConnection };
