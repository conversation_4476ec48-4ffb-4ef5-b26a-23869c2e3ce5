import { test, expect } from '../fixtures/auth';
import { onDemandPage } from '../pages/OnDemandPage';

test.describe('On-Demand Search page validations', () => {
  test('Navigate to On-Demand Search page and verify page elements', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');

    // 🔹 Navigate to On-Demand Search page
    await expect(onDemand.onDemandButton).toBeVisible({ timeout: 10000 });
    await expect(onDemand.onDemandButton).toHaveText('On-Demand Search');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Verify search page elements are visible
    await expect(onDemand.searchParametersPanel).toBeVisible();
    await expect(onDemand.searchParametersHeader).toBeVisible();
    await expect(onDemand.searchParametersHeader).toHaveText('Search Parameters');

    // 🔹 Verify search form fields are visible
    await expect(onDemand.productKeyField).toBeVisible();
    await expect(onDemand.vendorPartNumberField).toBeVisible();
    await expect(onDemand.vendorNameField).toBeVisible();
    await expect(onDemand.vendorProductDescriptionField).toBeVisible();
    await expect(onDemand.vizientContractCategoryField).toBeVisible();
  });

  test('Expand and collapse search parameters panel', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Verify panel is expanded by default
    await expect(onDemand.expansionPanel).toHaveClass(/mat-expanded/);

    // 🔹 Collapse the panel
    await onDemand.collapseSearchParameters();
    await expect(onDemand.expansionPanel).not.toHaveClass(/mat-expanded/);

    // 🔹 Expand the panel again
    await onDemand.expandSearchParameters();
    await expect(onDemand.expansionPanel).toHaveClass(/mat-expanded/);
  });

  test('Fill search form with Product Key and execute search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Fill Product Key field
    const testProductKey = 'TEST123';
    await onDemand.fillProductKey(testProductKey);
    await expect(onDemand.productKeyInput).toHaveValue(testProductKey);

    // 🔹 Execute search
    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      // 🔹 Wait for results or verify search was executed
      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();
        if (hasResults) {
          await expect(onDemand.resultsGrid).toBeVisible();
        }
      } catch {
        // Search may return no results, which is acceptable for testing
        console.log('Search executed but no results returned');
      }
    }
  });

  test('Fill search form with Vendor Part Number and execute search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Fill Vendor Part Number field
    const testPartNumber = 'PART456';
    await onDemand.fillVendorPartNumber(testPartNumber);
    await expect(onDemand.vendorPartNumberInput).toHaveValue(testPartNumber);

    // 🔹 Execute search
    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      // 🔹 Wait for results or verify search was executed
      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();
        if (hasResults) {
          await expect(onDemand.resultsGrid).toBeVisible();
        }
      } catch {
        // Search may return no results, which is acceptable for testing
        console.log('Search executed but no results returned');
      }
    }
  });

  test('Fill search form with Vendor Product Description and execute search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Fill Vendor Product Description field
    const testDescription = 'Medical Device';
    await onDemand.fillVendorProductDescription(testDescription);
    await expect(onDemand.vendorProductDescriptionInput).toHaveValue(testDescription);

    // 🔹 Execute search
    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      // 🔹 Wait for results or verify search was executed
      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();
        if (hasResults) {
          await expect(onDemand.resultsGrid).toBeVisible();
        }
      } catch {
        // Search may return no results, which is acceptable for testing
        console.log('Search executed but no results returned');
      }
    }
  });

  test('Select Vendor Name from dropdown and execute search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Click on Vendor Name field to open dropdown
    await onDemand.vendorNameField.click();

    // 🔹 Wait for dropdown to appear and select first available option
    try {
      await onDemand.vendorNameDropdown.waitFor({ state: 'visible', timeout: 5000 });
      const firstOption = onDemand.vendorNameDropdown.locator('li').first();
      if (await firstOption.isVisible()) {
        await firstOption.click();

        // 🔹 Execute search
        if (await onDemand.executeSearchButton.isVisible()) {
          await onDemand.executeSearch();

          // 🔹 Wait for results or verify search was executed
          try {
            await onDemand.waitForResults();
            const hasResults = await onDemand.hasResults();
            if (hasResults) {
              await expect(onDemand.resultsGrid).toBeVisible();
            }
          } catch {
            console.log('Search executed but no results returned');
          }
        }
      }
    } catch {
      console.log('Vendor Name dropdown not available or no options found');
    }
  });

  test('Select Vizient Contract Category from dropdown and execute search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Click on Vizient Contract Category field to open dropdown
    await onDemand.vizientContractCategoryField.click();

    // 🔹 Wait for dropdown to appear and select first available option
    try {
      await onDemand.vizientContractCategoryDropdown.waitFor({ state: 'visible', timeout: 5000 });
      const firstOption = onDemand.vizientContractCategoryDropdown.locator('li').first();
      if (await firstOption.isVisible()) {
        await firstOption.click();

        // 🔹 Execute search
        if (await onDemand.executeSearchButton.isVisible()) {
          await onDemand.executeSearch();

          // 🔹 Wait for results or verify search was executed
          try {
            await onDemand.waitForResults();
            const hasResults = await onDemand.hasResults();
            if (hasResults) {
              await expect(onDemand.resultsGrid).toBeVisible();
            }
          } catch {
            console.log('Search executed but no results returned');
          }
        }
      }
    } catch {
      console.log('Vizient Contract Category dropdown not available or no options found');
    }
  });

  test('Fill multiple search fields and execute combined search', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Fill multiple search fields
    await onDemand.fillProductKey('MULTI123');
    await onDemand.fillVendorPartNumber('MULTI456');
    await onDemand.fillVendorProductDescription('Multi Search Test');

    // 🔹 Verify all fields are filled
    await expect(onDemand.productKeyInput).toHaveValue('MULTI123');
    await expect(onDemand.vendorPartNumberInput).toHaveValue('MULTI456');
    await expect(onDemand.vendorProductDescriptionInput).toHaveValue('Multi Search Test');

    // 🔹 Execute search
    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      // 🔹 Wait for results or verify search was executed
      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();
        if (hasResults) {
          await expect(onDemand.resultsGrid).toBeVisible();
        }
      } catch {
        console.log('Multi-field search executed but no results returned');
      }
    }
  });

  test('Clear search form functionality', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Ensure search parameters are expanded
    await onDemand.expandSearchParameters();

    // 🔹 Fill search fields
    await onDemand.fillProductKey('CLEAR123');
    await onDemand.fillVendorPartNumber('CLEAR456');
    await onDemand.fillVendorProductDescription('Clear Test');

    // 🔹 Verify fields are filled
    await expect(onDemand.productKeyInput).toHaveValue('CLEAR123');
    await expect(onDemand.vendorPartNumberInput).toHaveValue('CLEAR456');
    await expect(onDemand.vendorProductDescriptionInput).toHaveValue('Clear Test');

    // 🔹 Clear the form
    if (await onDemand.clearButton.isVisible()) {
      await onDemand.clearSearchForm();

      // 🔹 Verify form is cleared
      const isEmpty = await onDemand.isSearchFormEmpty();
      expect(isEmpty).toBe(true);
    }
  });

  test('Search results grid interaction and filtering', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Perform a search that should return results
    await onDemand.expandSearchParameters();
    await onDemand.fillProductKey('TEST');

    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();

        if (hasResults) {
          // 🔹 Verify results grid is visible
          await expect(onDemand.resultsGrid).toBeVisible();

          // 🔹 Test keyword filtering if filter input is available
          if (await onDemand.keywordFilterInput.isVisible()) {
            await onDemand.filterResults('test');
            await expect(onDemand.keywordFilterInput).toHaveValue('test');
          }

          // 🔹 Test refresh functionality if available
          if (await onDemand.refreshButton.isVisible()) {
            await onDemand.refreshResults();
            await expect(onDemand.resultsGrid).toBeVisible();
          }
        }
      } catch {
        console.log('No results returned for grid interaction test');
      }
    }
  });

  test('Search results pagination functionality', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Perform a search that should return multiple pages of results
    await onDemand.expandSearchParameters();
    await onDemand.fillVendorProductDescription('device');

    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();

        if (hasResults) {
          // 🔹 Check if pagination controls are available
          if (await onDemand.paginationControls.isVisible()) {
            // 🔹 Get initial results count
            const initialCount = await onDemand.getResultsCount();
            expect(initialCount).toBeTruthy();

            // 🔹 Test next page if available
            if (await onDemand.nextPageButton.isEnabled()) {
              await onDemand.goToNextPage();
              await onDemand.waitForResults();
              await expect(onDemand.resultsGrid).toBeVisible();
            }

            // 🔹 Test previous page if available
            if (await onDemand.previousPageButton.isEnabled()) {
              await onDemand.goToPreviousPage();
              await onDemand.waitForResults();
              await expect(onDemand.resultsGrid).toBeVisible();
            }

            // 🔹 Test items per page change if dropdown is available
            if (await onDemand.itemsPerPageDropdown.isVisible()) {
              await onDemand.changeItemsPerPage('50');
              await onDemand.waitForResults();
              await expect(onDemand.resultsGrid).toBeVisible();
            }
          }
        }
      } catch {
        console.log('No paginated results available for pagination test');
      }
    }
  });

  test('Search results export functionality', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');
    await onDemand.navigateToOnDemandSearch();

    // 🔹 Perform a search
    await onDemand.expandSearchParameters();
    await onDemand.fillProductKey('EXPORT');

    if (await onDemand.executeSearchButton.isVisible()) {
      await onDemand.executeSearch();

      try {
        await onDemand.waitForResults();
        const hasResults = await onDemand.hasResults();

        if (hasResults) {
          // 🔹 Test export functionality if available
          if (await onDemand.exportButton.isVisible()) {
            // Set up download promise before clicking export
            const downloadPromise = page.waitForEvent('download', { timeout: 10000 });
            await onDemand.exportResults();

            try {
              const download = await downloadPromise;
              expect(download.suggestedFilename()).toBeTruthy();
            } catch {
              console.log('Export initiated but download may not have completed');
            }
          }

          // 🔹 Test download functionality if available
          if (await onDemand.downloadButton.isVisible()) {
            const downloadPromise = page.waitForEvent('download', { timeout: 10000 });
            await onDemand.downloadButton.click();

            try {
              const download = await downloadPromise;
              expect(download.suggestedFilename()).toBeTruthy();
            } catch {
              console.log('Download initiated but may not have completed');
            }
          }
        }
      } catch {
        console.log('No results available for export test');
      }
    }
  });

  test('Navigation between On-Demand Search and other pages', async ({ context }) => {
    const page = await context.newPage();
    const onDemand = new onDemandPage(page);

    await page.goto('https://mdmxref.test.vizientinc.com/');

    // 🔹 Navigate to On-Demand Search page
    await onDemand.navigateToOnDemandSearch();
    await expect(onDemand.searchParametersPanel).toBeVisible();

    // 🔹 Navigate back to home
    await onDemand.navigateToHome();
    await expect(onDemand.homeButton).toBeVisible();

    // 🔹 Navigate to On-Demand Search again
    await onDemand.navigateToOnDemandSearch();
    await expect(onDemand.searchParametersPanel).toBeVisible();
  });
});