# MDMXref E2E Test Environment Configuration
# Copy this file to .env and fill in the actual values

# Test Environment
TEST_ENVIRONMENT=test

# Test Database Configuration (Azure SQL Database)
DB_TEST_SERVER=vzn-eastus2-mdmxrefv3-test-sql-01.database.windows.net
DB_TEST_DATABASE=MDMXREFDB
DB_TEST_USER=
DB_TEST_PASSWORD=
DB_TEST_DOMAIN=vizientinc.com
DB_TEST_PORT=1433

# Staging Database Configuration
DB_STAGING_SERVER=mdmxref-staging-db.vizientinc.com
DB_STAGING_DATABASE=MDMXref_Staging
DB_STAGING_USER=mdmxref_staging_user
DB_STAGING_PASSWORD=your_staging_password_here
DB_STAGING_PORT=1433

# Production Database Configuration (Use with caution!)
DB_PROD_SERVER=mdmxref-prod-db.vizientinc.com
DB_PROD_DATABASE=MDMXref_Production
DB_PROD_USER=mdmxref_prod_readonly_user
DB_PROD_PASSWORD=your_prod_password_here
DB_PROD_PORT=1433

# Application URLs
APP_TEST_URL=https://mdmxref.test.vizientinc.com
APP_STAGING_URL=https://mdmxref.staging.vizientinc.com
APP_PROD_URL=https://mdmxref.vizientinc.com

# Test Configuration
TEST_TIMEOUT=30000
TEST_RETRY_COUNT=2
TEST_PARALLEL_WORKERS=1

# Database Connection Settings
DB_CONNECTION_TIMEOUT=30000
DB_REQUEST_TIMEOUT=30000
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_POOL_IDLE_TIMEOUT=30000

# Test Data Settings
TEST_DATA_CACHE_DURATION=300000
TEST_DATA_MAX_RECORDS=50

# Logging
LOG_LEVEL=info
LOG_DATABASE_QUERIES=false
