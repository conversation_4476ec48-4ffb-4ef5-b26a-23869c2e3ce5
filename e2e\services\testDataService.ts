// e2e/services/testDataService.ts
import { executeQuery, executeQueryFirst, connectToDatabase } from '../utils/database';

// Type definitions for test data
export interface ProductData {
  productKey: string;
  vendorPartNumber: string;
  vendorName: string;
  vendorProductDescription: string;
  vizientContractCategory: string;
  isActive: boolean;
  lastUpdated: Date;
}

export interface VendorData {
  vendorId: number;
  vendorName: string;
  vendorCode: string;
  isActive: boolean;
}

export interface ContractCategoryData {
  categoryId: number;
  categoryName: string;
  categoryCode: string;
  isActive: boolean;
}

export interface CrossReferenceData {
  crossReferenceId: number;
  inputProductKey: string;
  outputProductKey: string;
  inputVendorName: string;
  outputVendorName: string;
  confidenceLevel: string;
  isValid: boolean;
}

export class TestDataService {
  private static instance: TestDataService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): TestDataService {
    if (!TestDataService.instance) {
      TestDataService.instance = new TestDataService();
    }
    return TestDataService.instance;
  }

  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION);
  }

  private getCache<T>(key: string): T | null {
    if (this.isCacheValid(key)) {
      return this.cache.get(key) as T;
    }
    return null;
  }

  // Initialize connection
  public async initialize(): Promise<void> {
    await connectToDatabase();
  }

  // Get sample product data for testing
  public async getProductData(limit: number = 10): Promise<ProductData[]> {
    const cacheKey = `products_${limit}`;
    const cached = this.getCache<ProductData[]>(cacheKey);
    if (cached) return cached;

    const query = `
      SELECT TOP (@limit)
        p.ProductKey as productKey,
        p.VendorPartNumber as vendorPartNumber,
        v.VendorName as vendorName,
        p.VendorProductDescription as vendorProductDescription,
        cc.CategoryName as vizientContractCategory,
        p.IsActive as isActive,
        p.LastUpdated as lastUpdated
      FROM Products p
      INNER JOIN Vendors v ON p.VendorId = v.VendorId
      LEFT JOIN ContractCategories cc ON p.CategoryId = cc.CategoryId
      WHERE p.IsActive = 1
        AND p.ProductKey IS NOT NULL
        AND p.VendorPartNumber IS NOT NULL
        AND v.VendorName IS NOT NULL
      ORDER BY p.LastUpdated DESC
    `;

    const results = await executeQuery<ProductData>(query, { limit });
    this.setCache(cacheKey, results);
    return results;
  }

  // Get a single random product for testing
  public async getRandomProduct(): Promise<ProductData | null> {
    const query = `
      SELECT TOP 1
        p.ProductKey as productKey,
        p.VendorPartNumber as vendorPartNumber,
        v.VendorName as vendorName,
        p.VendorProductDescription as vendorProductDescription,
        cc.CategoryName as vizientContractCategory,
        p.IsActive as isActive,
        p.LastUpdated as lastUpdated
      FROM Products p
      INNER JOIN Vendors v ON p.VendorId = v.VendorId
      LEFT JOIN ContractCategories cc ON p.CategoryId = cc.CategoryId
      WHERE p.IsActive = 1
        AND p.ProductKey IS NOT NULL
        AND p.VendorPartNumber IS NOT NULL
        AND v.VendorName IS NOT NULL
      ORDER BY NEWID()
    `;

    return await executeQueryFirst<ProductData>(query);
  }

  // Get vendor data for dropdown testing
  public async getVendorData(limit: number = 20): Promise<VendorData[]> {
    const cacheKey = `vendors_${limit}`;
    const cached = this.getCache<VendorData[]>(cacheKey);
    if (cached) return cached;

    const query = `
      SELECT TOP (@limit)
        VendorId as vendorId,
        VendorName as vendorName,
        VendorCode as vendorCode,
        IsActive as isActive
      FROM Vendors
      WHERE IsActive = 1
        AND VendorName IS NOT NULL
        AND VendorName != ''
      ORDER BY VendorName
    `;

    const results = await executeQuery<VendorData>(query, { limit });
    this.setCache(cacheKey, results);
    return results;
  }

  // Get contract category data for dropdown testing
  public async getContractCategoryData(limit: number = 20): Promise<ContractCategoryData[]> {
    const cacheKey = `categories_${limit}`;
    const cached = this.getCache<ContractCategoryData[]>(cacheKey);
    if (cached) return cached;

    const query = `
      SELECT TOP (@limit)
        CategoryId as categoryId,
        CategoryName as categoryName,
        CategoryCode as categoryCode,
        IsActive as isActive
      FROM ContractCategories
      WHERE IsActive = 1
        AND CategoryName IS NOT NULL
        AND CategoryName != ''
      ORDER BY CategoryName
    `;

    const results = await executeQuery<ContractCategoryData>(query, { limit });
    this.setCache(cacheKey, results);
    return results;
  }

  // Get products by specific criteria for targeted testing
  public async getProductsByVendor(vendorName: string, limit: number = 5): Promise<ProductData[]> {
    const query = `
      SELECT TOP (@limit)
        p.ProductKey as productKey,
        p.VendorPartNumber as vendorPartNumber,
        v.VendorName as vendorName,
        p.VendorProductDescription as vendorProductDescription,
        cc.CategoryName as vizientContractCategory,
        p.IsActive as isActive,
        p.LastUpdated as lastUpdated
      FROM Products p
      INNER JOIN Vendors v ON p.VendorId = v.VendorId
      LEFT JOIN ContractCategories cc ON p.CategoryId = cc.CategoryId
      WHERE p.IsActive = 1
        AND v.VendorName = @vendorName
        AND p.ProductKey IS NOT NULL
        AND p.VendorPartNumber IS NOT NULL
      ORDER BY p.LastUpdated DESC
    `;

    return await executeQuery<ProductData>(query, { limit, vendorName });
  }

  // Get products by category for targeted testing
  public async getProductsByCategory(categoryName: string, limit: number = 5): Promise<ProductData[]> {
    const query = `
      SELECT TOP (@limit)
        p.ProductKey as productKey,
        p.VendorPartNumber as vendorPartNumber,
        v.VendorName as vendorName,
        p.VendorProductDescription as vendorProductDescription,
        cc.CategoryName as vizientContractCategory,
        p.IsActive as isActive,
        p.LastUpdated as lastUpdated
      FROM Products p
      INNER JOIN Vendors v ON p.VendorId = v.VendorId
      INNER JOIN ContractCategories cc ON p.CategoryId = cc.CategoryId
      WHERE p.IsActive = 1
        AND cc.CategoryName = @categoryName
        AND p.ProductKey IS NOT NULL
        AND p.VendorPartNumber IS NOT NULL
      ORDER BY p.LastUpdated DESC
    `;

    return await executeQuery<ProductData>(query, { limit, categoryName });
  }

  // Get cross-reference data for results validation
  public async getCrossReferenceData(limit: number = 10): Promise<CrossReferenceData[]> {
    const cacheKey = `crossrefs_${limit}`;
    const cached = this.getCache<CrossReferenceData[]>(cacheKey);
    if (cached) return cached;

    const query = `
      SELECT TOP (@limit)
        cr.CrossReferenceId as crossReferenceId,
        cr.InputProductKey as inputProductKey,
        cr.OutputProductKey as outputProductKey,
        iv.VendorName as inputVendorName,
        ov.VendorName as outputVendorName,
        cr.ConfidenceLevel as confidenceLevel,
        cr.IsValid as isValid
      FROM CrossReferences cr
      INNER JOIN Products ip ON cr.InputProductKey = ip.ProductKey
      INNER JOIN Products op ON cr.OutputProductKey = op.ProductKey
      INNER JOIN Vendors iv ON ip.VendorId = iv.VendorId
      INNER JOIN Vendors ov ON op.VendorId = ov.VendorId
      WHERE cr.IsValid = 1
        AND cr.InputProductKey IS NOT NULL
        AND cr.OutputProductKey IS NOT NULL
      ORDER BY cr.LastUpdated DESC
    `;

    const results = await executeQuery<CrossReferenceData>(query, { limit });
    this.setCache(cacheKey, results);
    return results;
  }

  // Search for products that should return results (for positive testing)
  public async getSearchableProducts(searchTerm: string, limit: number = 5): Promise<ProductData[]> {
    const query = `
      SELECT TOP (@limit)
        p.ProductKey as productKey,
        p.VendorPartNumber as vendorPartNumber,
        v.VendorName as vendorName,
        p.VendorProductDescription as vendorProductDescription,
        cc.CategoryName as vizientContractCategory,
        p.IsActive as isActive,
        p.LastUpdated as lastUpdated
      FROM Products p
      INNER JOIN Vendors v ON p.VendorId = v.VendorId
      LEFT JOIN ContractCategories cc ON p.CategoryId = cc.CategoryId
      WHERE p.IsActive = 1
        AND (
          p.ProductKey LIKE '%' + @searchTerm + '%'
          OR p.VendorPartNumber LIKE '%' + @searchTerm + '%'
          OR p.VendorProductDescription LIKE '%' + @searchTerm + '%'
          OR v.VendorName LIKE '%' + @searchTerm + '%'
        )
      ORDER BY p.LastUpdated DESC
    `;

    return await executeQuery<ProductData>(query, { limit, searchTerm });
  }

  // Clear cache (useful for testing)
  public clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  // Get cache statistics (for debugging)
  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Export singleton instance
export const testDataService = TestDataService.getInstance();

// Helper functions for easy access to test data
export class TestDataHelper {
  private static dataService = testDataService;

  // Initialize the service
  public static async initialize(): Promise<void> {
    await TestDataHelper.dataService.initialize();
  }

  // Get test data for different search scenarios
  public static async getProductKeyTestData(): Promise<string[]> {
    const products = await TestDataHelper.dataService.getProductData(5);
    return products.map(p => p.productKey).filter(key => key && key.length > 0);
  }

  public static async getVendorPartNumberTestData(): Promise<string[]> {
    const products = await TestDataHelper.dataService.getProductData(5);
    return products.map(p => p.vendorPartNumber).filter(part => part && part.length > 0);
  }

  public static async getVendorNameTestData(): Promise<string[]> {
    const vendors = await TestDataHelper.dataService.getVendorData(10);
    return vendors.map(v => v.vendorName).filter(name => name && name.length > 0);
  }

  public static async getProductDescriptionTestData(): Promise<string[]> {
    const products = await TestDataHelper.dataService.getProductData(5);
    return products
      .map(p => p.vendorProductDescription)
      .filter(desc => desc && desc.length > 0)
      .map(desc => desc.length > 50 ? desc.substring(0, 50) : desc); // Truncate long descriptions
  }

  public static async getContractCategoryTestData(): Promise<string[]> {
    const categories = await TestDataHelper.dataService.getContractCategoryData(10);
    return categories.map(c => c.categoryName).filter(name => name && name.length > 0);
  }

  // Get a complete test data set for a comprehensive search test
  public static async getCompleteTestDataSet(): Promise<{
    productKey: string;
    vendorPartNumber: string;
    vendorName: string;
    productDescription: string;
    contractCategory: string;
  } | null> {
    const product = await TestDataHelper.dataService.getRandomProduct();
    if (!product) return null;

    return {
      productKey: product.productKey,
      vendorPartNumber: product.vendorPartNumber,
      vendorName: product.vendorName,
      productDescription: product.vendorProductDescription?.length > 50
        ? product.vendorProductDescription.substring(0, 50)
        : product.vendorProductDescription,
      contractCategory: product.vizientContractCategory
    };
  }

  // Get test data that should return search results
  public static async getValidSearchData(): Promise<{
    productKeys: string[];
    vendorPartNumbers: string[];
    vendorNames: string[];
    productDescriptions: string[];
    contractCategories: string[];
  }> {
    const [productKeys, vendorPartNumbers, vendorNames, productDescriptions, contractCategories] = await Promise.all([
      TestDataHelper.getProductKeyTestData(),
      TestDataHelper.getVendorPartNumberTestData(),
      TestDataHelper.getVendorNameTestData(),
      TestDataHelper.getProductDescriptionTestData(),
      TestDataHelper.getContractCategoryTestData()
    ]);

    return {
      productKeys: productKeys.slice(0, 3),
      vendorPartNumbers: vendorPartNumbers.slice(0, 3),
      vendorNames: vendorNames.slice(0, 3),
      productDescriptions: productDescriptions.slice(0, 3),
      contractCategories: contractCategories.slice(0, 3)
    };
  }

  // Get test data for specific vendor
  public static async getVendorSpecificTestData(vendorName: string): Promise<ProductData[]> {
    return await TestDataHelper.dataService.getProductsByVendor(vendorName, 3);
  }

  // Get test data for specific category
  public static async getCategorySpecificTestData(categoryName: string): Promise<ProductData[]> {
    return await TestDataHelper.dataService.getProductsByCategory(categoryName, 3);
  }

  // Clear all cached data
  public static clearCache(): void {
    TestDataHelper.dataService.clearCache();
  }
}

// Export helper for easy access
export const testDataHelper = TestDataHelper;
