{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "allowJs": true, "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": false, "allowUnusedLabels": false, "types": ["node"]}, "include": ["e2e/**/*", "playwright.config.ts"], "exclude": ["node_modules", "dist", "reports", "artifacts", "test-results"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}