import { test, expect } from '../fixtures/auth';
import { navigateToCCCReport } from '../helpers/navigation';
import { expectTextsVisible } from '../helpers/assertions';
import { ReportsPage } from '../pages/reportsPage';

test.describe('Reports page validations', () => {
  test('check side menu and report grid', async ({ context }) => {
    const page = await context.newPage();
    const reports = new ReportsPage(page);

    await navigateToCCCReport(page);

    // Side menu validations
    await expect(reports.selectCategoryText).toBeVisible();
    await expect(reports.unspscOption).toBeVisible();
    await expect(reports.unspscCategory).toBeVisible();
    await reports.vccOption.click();
    await expect(reports.vizientContractCategory).toBeVisible();
    await reports.vscOption.click();
    await expect(reports.codeGroupAndSubcategory).toBeVisible();
    await expect(reports.vendorSelectionText).toBeVisible();
    await expect(reports.requiredHeading).toBeVisible();

    // Source controls
    await page.waitForTimeout(500);
    await expectTextsVisible(page, [
      'Derived',
      'Attribution',
      'List Vendors by # of products',
      'List Vendors alphabetically',
      'Same Vendor(s) for Input and Output',
      'Include the following sources',
    ]);

    await expect(page.getByRole('button', { name: 'Clear' })).toBeVisible();

    // Report results
    await expect(reports.backArrow).toBeVisible();
    await expect(reports.resultsHeading).toBeVisible();
    await expect(reports.resultsLabel).toBeVisible();
    await expect(reports.exportsLabel).toBeVisible();
    await expect(reports.keywordFilterInput).toBeVisible();
    await expect(reports.dashboardCustomize).toBeVisible();

    // Grid headers
    for (const header of reports.gridHeaders) {
      await expect(page.getByRole('button', { name: header, exact: true })).toBeVisible();
    }

    // Cleared state
    await expectTextsVisible(page, [
      'info_outline',
      'Please select search parameters to execute search',
    ]);

    // Pagination
    await expect(reports.itemsPerPageText).toBeVisible();
    await expect(reports.itemsPerPageValue).toBeVisible();
    await expect(reports.paginationLabel).toBeVisible();
  });
});

test.describe('Categorization Rank modal validations', () => {
  test('check Categorization Rank modal functionality and text', async ({ context }) => {
    const page = await context.newPage();
    const reports = new ReportsPage(page);

    await navigateToCCCReport(page);
    await reports.categorizationRankIcon.click({ force: true });

    await expect(
      page.getByRole('heading', { name: 'Categorization Rank Explanation' })
    ).toBeVisible({ timeout: 30000 });

    await expectTextsVisible(page, [
      'The Categorization Rank represents crosses that are supported by the greatest number of contributing sources (similar to Confidence Label), but with additional weighting based on alignment across several product categorization schemas (UNSPSC, PSC, and VSC).',
      'Crosses where both products are in the same UNSPSC, PSC, and VSC will therefore receive a greater Rank than crosses where one or more of those Categories differ.',
      'A Rank of 1 will always serve as the best available cross, dependent upon these factors.',
    ]);

    await reports.okButton.click();
  });
});

test.describe('Confidence Label modal validations', () => {
  test('check Confidence Label modal functionality and text', async ({ context }) => {
    const page = await context.newPage();
    const reports = new ReportsPage(page);

    await navigateToCCCReport(page);
    await reports.confidenceLabelIcon.click({ force: true });

    await expect(
      page.getByRole('heading', { name: 'Confidence Label Explanation' })
    ).toBeVisible({ timeout: 10000 });

    await expectTextsVisible(page, [
      'HIGH - Three or more unique sources have submitted the cross-reference.',
      'MEDIUM - Two unique sources have submitted the cross-reference.',
      'LOW - A single, unique source has submitted the cross-reference.',
      'NONE - Cross-reference was created by derivation or the cross-reference was made invalid.',
    ]);

    await reports.okButton.click();
  });
});
