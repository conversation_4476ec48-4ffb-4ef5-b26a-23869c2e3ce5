// e2e/utils/snapshot-report.ts
import fs from 'fs';
import path from 'path';

function copyDir(src: string, dest: string) {
  if (!fs.existsSync(dest)) fs.mkdirSync(dest, { recursive: true });
  for (const entry of fs.readdirSync(src)) {
    const s = path.join(src, entry);
    const d = path.join(dest, entry);
    const stat = fs.statSync(s);
    if (stat.isDirectory()) copyDir(s, d);
    else if (!entry.endsWith('.pdf')) { // 👈 skip the generic PDF
      fs.copyFileSync(s, d);
    }
  }
}

const latestDir = path.resolve('reports', 'latest');
const html = path.join(latestDir, 'index.html');
const pdf = path.join(latestDir, 'report.pdf');

if (!fs.existsSync(html)) {
  console.error('❌ No latest report found. Run tests first.');
  process.exit(1);
}

// timestamp for folder + PDF
const now = new Date();
const pad = (n: number) => String(n).padStart(2, '0');
const stamp = `${now.getFullYear()}-${pad(now.getMonth()+1)}-${pad(now.getDate())}_${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;

const archiveDir = path.resolve('reports', stamp);
copyDir(latestDir, archiveDir);

// copy the PDF with a dated filename
if (fs.existsSync(pdf)) {
  const datedPdf = path.join(archiveDir, `mdmxref-smoketest-${stamp}.pdf`);
  fs.copyFileSync(pdf, datedPdf);
  console.log(`📄 PDF archived as: ${datedPdf}`);
} else {
  console.warn('⚠️ No report.pdf found in latest; skipping PDF copy.');
}

console.log(`📦 Archived HTML report to: ${archiveDir}`);
