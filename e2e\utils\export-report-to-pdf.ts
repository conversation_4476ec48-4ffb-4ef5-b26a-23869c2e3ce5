import { chromium } from 'playwright';
import path from 'path';
import fs from 'fs';

(async () => {
  const htmlPath = path.resolve('reports', 'latest', 'index.html');
  const pdfPath = path.resolve('reports', 'latest', 'report.pdf');

  if (!fs.existsSync(htmlPath)) {
    console.error('❌ HTML report not found at reports/latest/index.html. Run tests first.');
    process.exit(1);
  }

  const browser = await chromium.launch();
  const page = await browser.newPage();

  const url = 'file:///' + htmlPath.replace(/\\/g, '/');
  await page.goto(url, { waitUntil: 'load' });
  await page.waitForTimeout(1500); // small settle time for charts/animations

  await page.pdf({
    path: pdfPath,
    format: 'A4',
    printBackground: true,
  });

  await browser.close();
  console.log(`✅ PDF saved at: ${pdfPath}`);
})();
