// e2e/utils/database.ts
import sql, { ConnectionPool, IResult } from 'mssql';
import { getMSSQLConfig } from '../config/database';

export class DatabaseConnection {
  private static instance: DatabaseConnection;
  private pool: ConnectionPool | null = null;
  private isConnected: boolean = false;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async connect(): Promise<void> {
    if (this.isConnected && this.pool) {
      return;
    }

    try {
      const config = getMSSQLConfig();
      this.pool = new ConnectionPool(config);
      await this.pool.connect();
      this.isConnected = true;
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.pool && this.isConnected) {
      try {
        await this.pool.close();
        this.isConnected = false;
        this.pool = null;
        console.log('✅ Database disconnected successfully');
      } catch (error) {
        console.error('❌ Database disconnection failed:', error);
        throw error;
      }
    }
  }

  public async query<T = any>(queryText: string, parameters?: Record<string, any>): Promise<T[]> {
    if (!this.pool || !this.isConnected) {
      await this.connect();
    }

    try {
      const request = this.pool!.request();
      
      // Add parameters if provided
      if (parameters) {
        Object.entries(parameters).forEach(([key, value]) => {
          request.input(key, value);
        });
      }

      const result: IResult<T> = await request.query(queryText);
      return result.recordset;
    } catch (error) {
      console.error('❌ Database query failed:', error);
      console.error('Query:', queryText);
      console.error('Parameters:', parameters);
      throw error;
    }
  }

  public async queryFirst<T = any>(queryText: string, parameters?: Record<string, any>): Promise<T | null> {
    const results = await this.query<T>(queryText, parameters);
    return results.length > 0 ? results[0] : null;
  }

  public async queryScalar<T = any>(queryText: string, parameters?: Record<string, any>): Promise<T | null> {
    const result = await this.queryFirst<any>(queryText, parameters);
    if (!result) return null;
    
    // Return the first column value
    const firstKey = Object.keys(result)[0];
    return result[firstKey];
  }

  public async testConnection(): Promise<boolean> {
    try {
      await this.connect();
      const result = await this.queryScalar<number>('SELECT 1 as test');
      return result === 1;
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      return false;
    }
  }

  // Helper method to safely execute queries with automatic connection management
  public async executeQuery<T = any>(
    queryText: string, 
    parameters?: Record<string, any>
  ): Promise<T[]> {
    try {
      await this.connect();
      return await this.query<T>(queryText, parameters);
    } catch (error) {
      console.error('❌ Query execution failed:', error);
      throw error;
    }
  }

  // Helper method for transactions
  public async executeTransaction<T>(
    callback: (transaction: sql.Transaction) => Promise<T>
  ): Promise<T> {
    if (!this.pool || !this.isConnected) {
      await this.connect();
    }

    const transaction = new sql.Transaction(this.pool!);
    
    try {
      await transaction.begin();
      const result = await callback(transaction);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Transaction failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const db = DatabaseConnection.getInstance();

// Helper functions for common operations
export async function connectToDatabase(): Promise<void> {
  await db.connect();
}

export async function disconnectFromDatabase(): Promise<void> {
  await db.disconnect();
}

export async function executeQuery<T = any>(
  queryText: string, 
  parameters?: Record<string, any>
): Promise<T[]> {
  return await db.executeQuery<T>(queryText, parameters);
}

export async function executeQueryFirst<T = any>(
  queryText: string, 
  parameters?: Record<string, any>
): Promise<T | null> {
  return await db.queryFirst<T>(queryText, parameters);
}

export async function executeQueryScalar<T = any>(
  queryText: string, 
  parameters?: Record<string, any>
): Promise<T | null> {
  return await db.queryScalar<T>(queryText, parameters);
}

// Cleanup function for test teardown
export async function cleanupDatabase(): Promise<void> {
  try {
    await db.disconnect();
  } catch (error) {
    console.warn('⚠️ Database cleanup warning:', error);
  }
}
