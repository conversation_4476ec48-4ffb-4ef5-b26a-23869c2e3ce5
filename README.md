MDMXref E2E Tests (Playwright)

Playwright end-to-end tests for MDMXref with:

🔐 Login bypass using storage state (admin.json)
📝 Test reports in HTML and PDF
📦 Archived reports with timestamped folders

⚙️ Prerequisites
Node.js 20+
Install dependencies:
    npm install


Install Playwright browsers (first time only):
    npx playwright install

🔐 Authentication Bypass
We skip manual login by reusing a saved session (storageState).
    File: e2e/storage/admin.json
    Ignored by Git (.gitignore) → each teammate must generate their own copy
    All tests use the shared admin test account (test/stage environments)

Generate the Auth File
    npm run save-admin-auth


This will:
    Open a browser
    Log in with the shared admin account
    Save the session to e2e/storage/admin.json

Re-generate When
    Tests redirect you to the login page
    admin.json is deleted
    Login flow changes (selectors, MFA, etc.)

▶️ Running Tests
    Run all specs for the testEnv project:
        npm run test:all
    Config: playwright.config.ts
    Tests will start already authenticated via admin.json

📊 Reports (HTML + PDF)
Playwright generates reports after each run:
Latest run:
    reports/latest/index.html (HTML report)
    reports/latest/report.pdf (PDF export)
Archived runs:
    reports/YYYY-MM-DD_HH-MM-SS/ (full copy of HTML + dated PDF)

Run Everything in One Go
    npm run test:all:pdf:snapshot

This will:
    Run tests
    Generate HTML report
    Export to PDF
    Archive into a timestamped folder

Run Individual Steps
# Run tests → HTML report in reports/latest/
    npm run test:all

# Export HTML → PDF in reports/latest/report.pdf
    npm run report:pdf

# Archive latest → timestamped folder with dated PDF
    npm run report:snapshot

📂 Folder Layout
reports/
  latest/
    index.html
    report.pdf
  2025-09-03_19-59-16/
    index.html
    ...
    mdmxref-smoketest-2025-09-03_19-59-16.pdf
artifacts/             # traces/screenshots on failure
e2e/storage/
  admin.json           # local login bypass (ignored by Git)

🚫 .gitignore (IMPORTANT)
Do not commit generated reports or auth files. Our .gitignore includes:
    node_modules/
    playwright-report/
    reports/
    artifacts/
    .auth/
    e2e/storage/

🛠️ NPM Scripts
    npm run save-admin-auth → Generate e2e/storage/admin.json
    npm run test:all → Run tests for testEnv
    npm run report:pdf → Export latest HTML report → PDF
    npm run report:snapshot → Archive latest into timestamped folder
    npm run test:all:pdf:snapshot → Run everything in one step

❓ Troubleshooting
    Tests show login page → Run npm run save-admin-auth
    PDF not generated → Ensure reports/latest/index.html exists, then run npm run report:pdf
    Too many archived folders → Delete old ones under reports/ (or we can add auto-cleanup later)