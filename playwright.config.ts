import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './e2e/specs',
  reporter: [
    ['html', { outputFolder: 'reports/latest', open: 'never' }],
  ],

  // Put traces/screenshots/videos here (not mixed with the HTML report)
  outputDir: 'artifacts',
  
  use: {
    baseURL: 'https://mdmxref.test.vizientinc.com',
    trace: 'retain-on-failure',
    viewport: { width: 1280, height: 720 },
    screenshot: 'only-on-failure',
    video: 'off',
  },

  projects: [
    {
      name: 'testEnv',
      use: {
        browserName: 'chromium',
        headless: true,
        storageState: './e2e/storage/admin.json',
      },
    },
    {
      name: 'debug',
      use: {
        browserName: 'chromium',
        headless: false,
        storageState: './e2e/storage/admin.json',
        launchOptions: {
          slowMo: 500,
        },
      },
    },
  ],
});