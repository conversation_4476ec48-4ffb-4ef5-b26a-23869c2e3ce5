# Copilot Instructions for Xref-e2e Playwright Project

## Project Overview
- This is an end-to-end (E2E) testing project using [<PERSON><PERSON>](https://playwright.dev/) for UI automation.
- The main goal is to validate SSO login flows and other critical user journeys for the `mdmxref.test.vizientinc.com` web application.
- All tests are located in the `tests/` directory. Each spec file targets a specific flow or feature.

## Architecture & Patterns
- **Test Structure:**
  - Each test file (e.g., `login.spec.ts`) uses <PERSON><PERSON>'s `test` and `expect` APIs.
  - Tests are written in TypeScript and follow <PERSON><PERSON>'s recommended patterns.
  - Authentication setup logic may be separated into files like `auth.setup.ts` for reuse.
- **SSO Flow Example:**
  - Navigate to the app URL, wait for SSO redirect, fill in credentials, and assert successful login.
  - Example selectors: `#idp-discovery-username`, `.button.button-primary`, `#okta-signin-password`, `#okta-signin-submit`.

## Developer Workflows
- **Run Tests:**
  - Use Playwright CLI: `npx playwright test` (runs all tests in `tests/`)
  - To run a specific test: `npx playwright test tests/login.spec.ts`
  - Test results are saved in the `test-results/` directory.
- **Debugging:**
  - Use Playwright's debug mode: `npx playwright test --debug`
  - For headed mode: `npx playwright test --headed`
- **Configuration:**
  - Main config is in `playwright.config.ts`. Adjust base URLs, timeouts, and browser settings here.

## Conventions & Practices
- **Selectors:**
  - Prefer stable selectors (IDs, data attributes) over classes for element targeting.
  - Credentials and sensitive data are hardcoded in some tests; consider using environment variables for security.
- **Test Isolation:**
  - Each test should be independent and not rely on state from previous tests.
- **External Dependencies:**
  - Relies on Okta SSO (`sit-login.alliancewebs.net`) and the main app (`mdmxref.test.vizientinc.com`).
  - Network stability and test account validity are critical for successful runs.

## Key Files & Directories
- `tests/` — All test specs and setup scripts
- `playwright.config.ts` — Playwright configuration
- `package.json` — Scripts and dependencies
- `test-results/` — Output from test runs

## Example: SSO Login Test
```typescript
// tests/login.spec.ts
import { test, expect } from '@playwright/test';
test('SSO login flow', async ({ page }) => {
  await page.goto('https://mdmxref.test.vizientinc.com/');
  await page.waitForURL('https://sit-login.alliancewebs.net/**');
  await page.fill('#idp-discovery-username', '<EMAIL>');
  await page.click('.button.button-primary');
  await page.fill('#okta-signin-password', '#Demo2022');
  await page.click('#okta-signin-submit');
  await page.waitForURL('https://mdmxref.test.vizientinc.com/**');
  // Optionally, assert login success
});
```

---

**If any conventions, workflows, or integration points are unclear or missing, please provide feedback so this guide can be improved.**
