// e2e/utils/run-tests-with-report.ts
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

// Create a unique report name with date
const reportName = `mdmxref-smoketest-${new Date().toISOString().split('T')[0]}`;
const reportDir = path.resolve('reports', reportName);

// Ensure folder exists
fs.mkdirSync(reportDir, { recursive: true });

// 1️⃣ Run tests and save HTML report
console.log(`🏃 Running tests and saving report to: ${reportDir}`);
execSync(`npx playwright test --reporter=html --output=${reportDir}`, { stdio: 'inherit' });

// 2️⃣ Generate PDF from the HTML report
console.log(`📄 Exporting PDF...`);
execSync(`npx tsx e2e/utils/export-report-to-pdf.ts ${reportName}`, { stdio: 'inherit' });

console.log(`✅ Done! Report available at: ${reportDir}`);

