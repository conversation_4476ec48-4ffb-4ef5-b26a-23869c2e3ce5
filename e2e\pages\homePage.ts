// e2e/pages/HomePage.ts
import { Page, Locator } from '@playwright/test';

export class HomePage {
  readonly page: Page;

  // ✅ Selectors for Home page elements
  readonly headerTitle: Locator;
  readonly homeButton: Locator;

  constructor(page: Page) {
    this.page = page;

    // Using current selectors — can swap with data-testlabel later
    this.headerTitle = page.locator('span[data-header-title]');
    this.homeButton = page.getByRole('button', { name: 'Home' });
  }
}