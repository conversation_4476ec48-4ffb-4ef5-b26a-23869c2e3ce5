// e2e/pages/onDemandPage.ts
import { Page, Locator } from '@playwright/test';

export class onDemandPage {
  readonly page: Page;

  // Navigation elements
  readonly onDemandButton: Locator;
  readonly homeButton: Locator;

  // Search Parameters Panel
  readonly searchParametersButton: Locator;
  readonly searchParametersPanel: Locator;
  readonly searchParametersHeader: Locator;
  readonly expansionPanel: Locator;

  // Search Form Fields
  readonly productKeyInput: Locator;
  readonly productKeyField: Locator;
  readonly vendorPartNumberInput: Locator;
  readonly vendorPartNumberField: Locator;
  readonly vendorNameInput: Locator;
  readonly vendorNameField: Locator;
  readonly vendorNameDropdown: Locator;
  readonly vendorProductDescriptionInput: Locator;
  readonly vendorProductDescriptionField: Locator;
  readonly vizientContractCategoryInput: Locator;
  readonly vizientContractCategoryField: Locator;
  readonly vizientContractCategoryDropdown: Locator;

  // Search Action Buttons
  readonly executeSearchButton: Locator;
  readonly clearButton: Locator;
  readonly resetButton: Locator;

  // Results Section
  readonly resultsSection: Locator;
  readonly resultsGrid: Locator;
  readonly resultsTable: Locator;
  readonly noResultsMessage: Locator;
  readonly loadingSpinner: Locator;

  // Grid Controls
  readonly keywordFilterInput: Locator;
  readonly refreshButton: Locator;
  readonly exportButton: Locator;
  readonly downloadButton: Locator;

  // Pagination
  readonly paginationControls: Locator;
  readonly itemsPerPageDropdown: Locator;
  readonly nextPageButton: Locator;
  readonly previousPageButton: Locator;
  readonly firstPageButton: Locator;
  readonly lastPageButton: Locator;
  readonly paginationInfo: Locator;

  // Grid Headers (common columns based on cross-reference functionality)
  readonly gridHeaders: string[];

  constructor(page: Page) {
    this.page = page;

    // Navigation
    this.onDemandButton = page.getByRole('button', { name: 'On-Demand Search' });
    this.homeButton = page.getByRole('button', { name: 'Home' });

    // Search Parameters Panel
    this.searchParametersButton = page.getByRole('button', { name: 'Search Parameters' });
    this.searchParametersPanel = page.locator('#expansionPanel');
    this.searchParametersHeader = page.getByText('Search Parameters');
    this.expansionPanel = page.locator('mat-expansion-panel');

    // Search Form Fields - using IDs from HTML
    this.productKeyInput = page.locator('#productKey input[placeholder="Key"]');
    this.productKeyField = page.locator('#productKey');
    this.vendorPartNumberInput = page.locator('#vendorPartNumber input[placeholder="Part Number"]');
    this.vendorPartNumberField = page.locator('#vendorPartNumber');
    this.vendorNameInput = page.locator('#vendorName input');
    this.vendorNameField = page.locator('#vendorName');
    this.vendorNameDropdown = page.locator('#vendorName .dropdown-list');
    this.vendorProductDescriptionInput = page.locator('#vendorProductDescription input[placeholder="Part Description"]');
    this.vendorProductDescriptionField = page.locator('#vendorProductDescription');
    this.vizientContractCategoryInput = page.locator('#productSpendCategory input');
    this.vizientContractCategoryField = page.locator('#productSpendCategory');
    this.vizientContractCategoryDropdown = page.locator('#productSpendCategory .dropdown-list');

    // Search Action Buttons
    this.executeSearchButton = page.getByRole('button', { name: 'Execute Search' });
    this.clearButton = page.getByRole('button', { name: 'Clear' });
    this.resetButton = page.getByRole('button', { name: 'Reset' });

    // Results Section
    this.resultsSection = page.locator('[data-results-section]');
    this.resultsGrid = page.locator('mat-table, table');
    this.resultsTable = page.locator('table');
    this.noResultsMessage = page.getByText('No results found');
    this.loadingSpinner = page.locator('mat-progress-spinner');

    // Grid Controls
    this.keywordFilterInput = page.getByRole('textbox', { name: 'Type keyword to filter results' });
    this.refreshButton = page.getByRole('button', { name: 'Refresh' });
    this.exportButton = page.getByRole('button', { name: 'Export' });
    this.downloadButton = page.getByRole('button', { name: 'Download' });

    // Pagination
    this.paginationControls = page.locator('mat-paginator');
    this.itemsPerPageDropdown = page.locator('mat-select[aria-label="Items per page"]');
    this.nextPageButton = page.getByRole('button', { name: 'Next page' });
    this.previousPageButton = page.getByRole('button', { name: 'Previous page' });
    this.firstPageButton = page.getByRole('button', { name: 'First page' });
    this.lastPageButton = page.getByRole('button', { name: 'Last page' });
    this.paginationInfo = page.locator('.mat-paginator-range-label');

    // Common grid headers based on cross-reference functionality
    this.gridHeaders = [
      'Product Key',
      'Vendor Part Number',
      'Vendor Name',
      'Vendor Product Description',
      'Vizient Contract Category',
      'Cross-Reference Status',
      'Last Updated',
      'Actions'
    ];
  }

  // Navigation methods
  async navigateToOnDemandSearch() {
    await this.onDemandButton.click();
  }

  async navigateToHome() {
    await this.homeButton.click();
  }

  // Search Parameters methods
  async expandSearchParameters() {
    if (await this.expansionPanel.getAttribute('class') && !await this.expansionPanel.getAttribute('class')?.includes('mat-expanded')) {
      await this.searchParametersHeader.click();
    }
  }

  async collapseSearchParameters() {
    if (await this.expansionPanel.getAttribute('class') && await this.expansionPanel.getAttribute('class')?.includes('mat-expanded')) {
      await this.searchParametersHeader.click();
    }
  }

  // Search form interaction methods
  async fillProductKey(value: string) {
    await this.productKeyInput.fill(value);
  }

  async fillVendorPartNumber(value: string) {
    await this.vendorPartNumberInput.fill(value);
  }

  async fillVendorProductDescription(value: string) {
    await this.vendorProductDescriptionInput.fill(value);
  }

  async selectVendorName(vendorName: string) {
    await this.vendorNameField.click();
    await this.vendorNameDropdown.waitFor({ state: 'visible' });
    await this.page.getByText(vendorName, { exact: true }).click();
  }

  async selectVizientContractCategory(category: string) {
    await this.vizientContractCategoryField.click();
    await this.vizientContractCategoryDropdown.waitFor({ state: 'visible' });
    await this.page.getByText(category, { exact: true }).click();
  }

  // Search action methods
  async executeSearch() {
    await this.executeSearchButton.click();
  }

  async clearSearchForm() {
    await this.clearButton.click();
  }

  async resetSearchForm() {
    await this.resetButton.click();
  }

  // Results interaction methods
  async waitForResults() {
    await this.loadingSpinner.waitFor({ state: 'hidden', timeout: 30000 });
    await this.resultsGrid.waitFor({ state: 'visible', timeout: 10000 });
  }

  async filterResults(keyword: string) {
    await this.keywordFilterInput.fill(keyword);
  }

  async refreshResults() {
    await this.refreshButton.click();
  }

  async exportResults() {
    await this.exportButton.click();
  }

  // Pagination methods
  async goToNextPage() {
    await this.nextPageButton.click();
  }

  async goToPreviousPage() {
    await this.previousPageButton.click();
  }

  async goToFirstPage() {
    await this.firstPageButton.click();
  }

  async goToLastPage() {
    await this.lastPageButton.click();
  }

  async changeItemsPerPage(itemCount: string) {
    await this.itemsPerPageDropdown.click();
    await this.page.getByText(itemCount, { exact: true }).click();
  }

  // Validation methods
  async getResultsCount(): Promise<string> {
    return await this.paginationInfo.textContent() || '';
  }

  async isSearchFormEmpty(): Promise<boolean> {
    const productKey = await this.productKeyInput.inputValue();
    const vendorPartNumber = await this.vendorPartNumberInput.inputValue();
    const vendorDescription = await this.vendorProductDescriptionInput.inputValue();

    return !productKey && !vendorPartNumber && !vendorDescription;
  }

  async hasResults(): Promise<boolean> {
    try {
      await this.resultsGrid.waitFor({ state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
}