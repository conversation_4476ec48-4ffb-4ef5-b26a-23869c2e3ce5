// e2e/utils/save-auth-admin.ts
import { chromium } from '@playwright/test';
import fs from 'fs';
import path from 'path';

(async () => {
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  await page.goto('https://mdmxref.test.vizientinc.com/');
  await page.waitForURL('https://sit-login.alliancewebs.net/**', { timeout: 30000 });

  await page.waitForSelector('#idp-discovery-username');
  await page.fill('#idp-discovery-username', '<EMAIL>');
  await page.click('.button.button-primary');

  await page.waitForSelector('#okta-signin-password');
  await page.fill('#okta-signin-password', '#Demo2022');
  await page.click('#okta-signin-submit');

  await page.waitForURL('https://mdmxref.test.vizientinc.com/**', { timeout: 30000 });
  await page.waitForSelector('span[data-header-title]');

  // ✅ Ensure the folder exists before writing the file
  fs.mkdirSync(path.resolve('e2e/storage'), { recursive: true });

  // Save session to file
  await context.storageState({ path: 'e2e/storage/admin.json' });

  await browser.close();
})();